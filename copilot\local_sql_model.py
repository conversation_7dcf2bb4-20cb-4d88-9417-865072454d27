"""
SQLCoder-7B Model - SQL generation using the local SQLCoder-7B model
"""

import os
import torch
import warnings

# Suppress warnings about torchvision
warnings.filterwarnings("ignore", category=UserWarning)

try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    TRANSFORMERS_AVAILABLE = True
except Exception as e:
    print(f"Warning: Could not import transformers: {e}")
    TRANSFORMERS_AVAILABLE = False

class LocalSQLModel:
    def __init__(self, model_path="./sqlcoder-7b"):
        """
        Initialize SQLCoder-7B model from local directory
        """
        self.model_path = model_path
        self.tokenizer = None
        self.model = None
        self.model_available = False

        if not TRANSFORMERS_AVAILABLE:
            print("Transformers library not available. Using fallback SQL generation.")
            return

        print(f"Loading SQLCoder-7B model from {model_path}...")

        try:
            # Load tokenizer and model from local directory
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_path,
                local_files_only=True,
                trust_remote_code=True
            )

            self.model = AutoModelForCausalLM.from_pretrained(
                model_path,
                local_files_only=True,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )

            # Set padding token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token

            self.model_available = True
            print("SQLCoder-7B model loaded successfully!")

        except Exception as e:
            print(f"Error loading SQLCoder-7B model: {e}")
            print("Falling back to simple SQL generation...")
            self.model_available = False
    
    def generate_sql(self, schema_context, user_question):
        """Generate SQL query from natural language question using SQLCoder-7B"""

        if not self.model_available:
            return self._fallback_sql_generation(schema_context, user_question)

        # Create the prompt in SQLCoder format
        prompt = self._create_sqlcoder_prompt(schema_context, user_question)

        try:
            # Tokenize the prompt
            inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=2048)

            # Move to GPU if available
            if torch.cuda.is_available():
                inputs = {k: v.cuda() for k, v in inputs.items()}
                self.model = self.model.cuda()

            # Generate SQL
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=200,
                    temperature=0.1,
                    do_sample=True,
                    top_p=0.9,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )

            # Decode the generated SQL
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            sql_query = self._extract_sql_from_generated_text(generated_text, prompt)

            return sql_query

        except Exception as e:
            print(f"Error generating SQL with SQLCoder-7B: {e}")
            return self._fallback_sql_generation(schema_context, user_question)

    def _create_sqlcoder_prompt(self, schema_context, user_question):
        """Create prompt in SQLCoder format"""
        prompt = f"""### Task
Generate a SQL query to answer this question: `{user_question}`

### Database Schema
{schema_context}

### SQL
```sql
"""
        return prompt

    def _extract_sql_from_generated_text(self, generated_text, original_prompt):
        """Extract SQL query from the generated text"""
        # Remove the original prompt
        if original_prompt in generated_text:
            sql_part = generated_text.replace(original_prompt, "").strip()
        else:
            sql_part = generated_text.strip()

        # Look for SQL between ```sql and ``` or just take the first line
        if "```sql" in sql_part:
            sql_start = sql_part.find("```sql") + 6
            sql_end = sql_part.find("```", sql_start)
            if sql_end != -1:
                sql_query = sql_part[sql_start:sql_end].strip()
            else:
                sql_query = sql_part[sql_start:].strip()
        else:
            # Take the first non-empty line
            lines = sql_part.split('\n')
            sql_query = ""
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('--'):
                    sql_query = line
                    break

        # Clean up the SQL
        sql_query = sql_query.strip()
        if not sql_query.endswith(';'):
            sql_query += ';'

        return sql_query
    def _fallback_sql_generation(self, schema_context, user_question):
        """Simple fallback SQL generation when model is not available"""
        question_lower = user_question.lower()

        # Basic pattern matching for Northwind database
        if 'customers' in question_lower and 'germany' in question_lower:
            return "SELECT * FROM customers WHERE country = 'Germany';"
        elif 'customers' in question_lower and 'usa' in question_lower:
            return "SELECT * FROM customers WHERE country = 'USA';"
        elif 'customers' in question_lower:
            return "SELECT * FROM customers LIMIT 10;"
        elif 'orders' in question_lower and 'date' in question_lower:
            return "SELECT * FROM orders ORDER BY order_date DESC LIMIT 10;"
        elif 'orders' in question_lower:
            return "SELECT * FROM orders LIMIT 10;"
        elif 'products' in question_lower:
            return "SELECT * FROM products LIMIT 10;"
        elif 'employees' in question_lower:
            return "SELECT * FROM employees LIMIT 10;"
        else:
            # Default: show customers
            return "SELECT * FROM customers LIMIT 5;"

# Test the model
if __name__ == "__main__":
    model = LocalSQLModel("./sqlcoder-7b")

    schema = """Table: Customers
  - CustomerID (TEXT)
  - CompanyName (TEXT)
  - ContactName (TEXT)
  - Country (TEXT)

Table: Orders
  - OrderID (INTEGER)
  - CustomerID (TEXT)
  - OrderDate (TEXT)
  - ShipCountry (TEXT)"""

    questions = [
        "List all customers",
        "Show orders from USA",
        "Count orders by country",
        "What customers are from Germany?"
    ]

    for question in questions:
        print(f"\nQuestion: {question}")
        sql = model.generate_sql(schema, question)
        print(f"SQL: {sql}")
