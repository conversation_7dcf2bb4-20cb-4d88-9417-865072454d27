"""
Local SQL Model - A lightweight SQL generation using rule-based patterns and optional API calls
"""

import re
import sqlite3
import json
import requests

class LocalSQLModel:
    def __init__(self, model_name="rule-based"):
        """
        Initialize local SQL model
        Options for model_name:
        - "rule-based" (fast, pattern-based)
        - "ollama" (if you have Ollama running locally)
        - "huggingface-api" (free inference API)
        """
        self.model_name = model_name
        self.use_external_model = False

        if model_name == "ollama":
            self.use_external_model = True
            self.api_url = "http://localhost:11434/api/generate"
            print("Using Ollama local API (make sure Ollama is running)")
        elif model_name == "huggingface-api":
            self.use_external_model = True
            self.api_url = "https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium"
            print("Using Hugging Face Inference API")
        else:
            print("Using rule-based SQL generation (fast and reliable)")
    
    def generate_sql(self, schema_context, user_question):
        """Generate SQL query from natural language question"""

        if self.use_external_model:
            try:
                return self._generate_with_external_model(schema_context, user_question)
            except Exception as e:
                print(f"External model failed: {e}")
                print("Falling back to rule-based generation")

        return self._rule_based_generation(schema_context, user_question)

    def _generate_with_external_model(self, schema_context, user_question):
        """Generate SQL using external model API"""
        prompt = f"""Convert this natural language question to SQL.

Database Schema:
{schema_context}

Question: {user_question}

SQL Query:"""

        if self.model_name == "ollama":
            # Ollama API call
            payload = {
                "model": "codellama",  # or "llama2", "mistral"
                "prompt": prompt,
                "stream": False
            }
            response = requests.post(self.api_url, json=payload, timeout=30)
            if response.status_code == 200:
                result = response.json()
                sql = self._extract_sql_from_text(result.get('response', ''))
                return self._clean_sql(sql)

        elif self.model_name == "huggingface-api":
            # Hugging Face Inference API
            headers = {"Authorization": "Bearer YOUR_HF_TOKEN"}  # Optional
            payload = {"inputs": prompt}
            response = requests.post(self.api_url, headers=headers, json=payload, timeout=30)
            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    sql = self._extract_sql_from_text(result[0].get('generated_text', ''))
                    return self._clean_sql(sql)

        raise Exception("External model API call failed")
    
    def _extract_sql_from_text(self, text):
        """Extract SQL from generated text"""
        # Look for SQL keywords
        sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE']

        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if any(line.upper().startswith(keyword) for keyword in sql_keywords):
                # Found a SQL line
                if ';' in line:
                    return line.split(';')[0] + ';'
                else:
                    return line + ';'

        # If no clear SQL found, return the first non-empty line
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#') and not line.startswith('--'):
                return line + ';' if not line.endswith(';') else line

        return "SELECT 1; -- Could not extract SQL"
    
    def _clean_sql(self, sql_query):
        """Clean and validate SQL query"""
        # Remove extra whitespace
        sql_query = ' '.join(sql_query.split())
        
        # Ensure it starts with SELECT, INSERT, UPDATE, or DELETE
        sql_upper = sql_query.upper()
        if not any(sql_upper.startswith(cmd) for cmd in ['SELECT', 'INSERT', 'UPDATE', 'DELETE']):
            sql_query = 'SELECT ' + sql_query
        
        # Ensure it ends with semicolon
        if not sql_query.endswith(';'):
            sql_query += ';'
        
        return sql_query
    
    def _is_valid_sql_structure(self, sql_query):
        """Basic validation of SQL structure"""
        sql_upper = sql_query.upper()
        
        # Must start with valid SQL command
        valid_starts = ['SELECT', 'INSERT', 'UPDATE', 'DELETE']
        if not any(sql_upper.startswith(cmd) for cmd in valid_starts):
            return False
        
        # Must contain FROM for SELECT statements
        if sql_upper.startswith('SELECT') and 'FROM' not in sql_upper:
            return False
        
        # Basic syntax check - balanced parentheses
        if sql_query.count('(') != sql_query.count(')'):
            return False
        
        return True
    
    def _rule_based_generation(self, schema_context, user_question):
        """Rule-based SQL generation as fallback"""
        question_lower = user_question.lower()
        
        # Extract table names from schema
        tables = re.findall(r'Table: (\w+)', schema_context)
        
        # Simple pattern matching - order matters!
        # Check for more specific patterns first

        if ('over' in question_lower or 'greater than' in question_lower or '>' in question_lower) and ('user' in question_lower or 'customer' in question_lower or 'who' in question_lower):
            # Extract number from question
            numbers = re.findall(r'\d+', user_question)
            if numbers:
                threshold = numbers[0]
                return f"SELECT DISTINCT u.name FROM users u JOIN orders o ON u.id = o.user_id WHERE o.price > {threshold};"

        elif 'over' in question_lower or 'greater than' in question_lower or '>' in question_lower:
            # Just orders over a threshold
            numbers = re.findall(r'\d+', user_question)
            if numbers:
                threshold = numbers[0]
                return f"SELECT * FROM orders WHERE price > {threshold};"

        elif 'total' in question_lower and ('spending' in question_lower or 'price' in question_lower):
            return "SELECT u.name, SUM(o.price) as total_spending FROM users u LEFT JOIN orders o ON u.id = o.user_id GROUP BY u.id, u.name;"

        elif 'count' in question_lower and 'order' in question_lower:
            return "SELECT u.name, COUNT(o.id) as order_count FROM users u LEFT JOIN orders o ON u.id = o.user_id GROUP BY u.id, u.name;"

        elif 'expensive' in question_lower or 'highest' in question_lower or 'most' in question_lower:
            return "SELECT * FROM orders ORDER BY price DESC LIMIT 1;"

        elif 'all users' in question_lower or 'list users' in question_lower:
            return "SELECT * FROM users;"

        elif 'all orders' in question_lower or 'list orders' in question_lower:
            return "SELECT * FROM orders;"
        
        elif 'who ordered' in question_lower or 'who bought' in question_lower:
            # Extract product name if mentioned
            words = user_question.split()
            for i, word in enumerate(words):
                if word.lower() in ['ordered', 'bought'] and i + 1 < len(words):
                    product = words[i + 1].strip('?.,!').title()
                    return f"SELECT u.name FROM users u JOIN orders o ON u.id = o.user_id WHERE o.product LIKE '%{product}%';"
            return "SELECT u.name, o.product FROM users u JOIN orders o ON u.id = o.user_id;"

        elif 'join' in question_lower or ('user' in question_lower and 'order' in question_lower):
            return "SELECT u.name, o.product, o.price FROM users u JOIN orders o ON u.id = o.user_id;"
        
        # Default: show some data
        if 'users' in tables:
            return "SELECT * FROM users LIMIT 5;"
        elif tables:
            return f"SELECT * FROM {tables[0]} LIMIT 5;"
        else:
            return "SELECT 1; -- No tables found in schema"

# Test the model
if __name__ == "__main__":
    model = LocalSQLModel("rule-based")

    schema = """Table: users
  - id (INTEGER)
  - name (TEXT)
  - email (TEXT)

Table: orders
  - id (INTEGER)
  - user_id (INTEGER)
  - product (TEXT)
  - price (REAL)"""

    questions = [
        "List all users",
        "Show total spending per user",
        "Find orders over 100",
        "Count orders by user",
        "Show me all orders",
        "What is the most expensive order?"
    ]

    for question in questions:
        print(f"\nQuestion: {question}")
        sql = model.generate_sql(schema, question)
        print(f"SQL: {sql}")
