#!/usr/bin/env python3
"""
SQL Copilot - CLI and Web Interface
A tool to generate SQL queries using natural language with LLM assistance
"""

import os
import argparse
from copilot.query_assistant import SQLCopilot

def cli_interface():
    """Command line interface for SQL Copilot"""
    parser = argparse.ArgumentParser(description='SQL Copilot - Generate SQL queries from natural language')
    parser.add_argument('--db', default='data/dummy.db', help='Path to SQLite database file')
    parser.add_argument('--question', '-q', help='Natural language question to convert to SQL')
    parser.add_argument('--interactive', '-i', action='store_true', help='Start interactive mode')
    
    args = parser.parse_args()
    
    # Check if database exists
    if not os.path.exists(args.db):
        print(f"Database not found at {args.db}")
        print("Please run: python data/create_dummy_db.py")
        return
    
    # Initialize SQL Copilot
    copilot = SQLCopilot(args.db)
    
    if args.question:
        # Single question mode
        print(f"Question: {args.question}")
        sql_query, results = copilot.ask_and_execute(args.question)
        print(f"Generated SQL: {sql_query}")
        print(f"Results: {results}")
    
    elif args.interactive:
        # Interactive mode
        print("SQL Copilot Interactive Mode")
        print("Type 'quit' or 'exit' to stop")
        print("=" * 50)
        
        while True:
            try:
                question = input("\nEnter your question: ").strip()
                
                if question.lower() in ['quit', 'exit', 'q']:
                    print("Goodbye!")
                    break
                
                if not question:
                    continue
                
                print(f"\nQuestion: {question}")
                sql_query = copilot.ask_sql_copilot(question)
                print(f"Generated SQL: {sql_query}")
                
                # Ask if user wants to execute
                execute = input("Execute this query? (y/n): ").strip().lower()
                if execute in ['y', 'yes']:
                    results = copilot.execute_query(sql_query)
                    print(f"Results: {results}")
                
            except KeyboardInterrupt:
                print("\nGoodbye!")
                break
            except Exception as e:
                print(f"Error: {e}")
    
    else:
        # Show help and example
        print("SQL Copilot")
        print("=" * 20)
        print("Usage examples:")
        print("  python app.py -q \"List all users who placed an order over 100\"")
        print("  python app.py --interactive")
        print("  python app.py --db path/to/your/database.db -i")
        print("\nDatabase schema:")
        print(copilot.schema_context)

def web_interface():
    """Simple web interface using Flask (optional)"""
    try:
        from flask import Flask, request, jsonify, render_template_string
        
        app = Flask(__name__)
        copilot = SQLCopilot('data/dummy.db')
        
        HTML_TEMPLATE = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>SQL Copilot</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .container { max-width: 800px; margin: 0 auto; }
                textarea { width: 100%; height: 100px; margin: 10px 0; }
                button { padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer; }
                .result { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
                .schema { background: #e8f4f8; padding: 15px; margin: 10px 0; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>SQL Copilot</h1>
                <div class="schema">
                    <h3>Database Schema:</h3>
                    <pre>{{ schema }}</pre>
                </div>
                <form id="queryForm">
                    <label for="question">Ask a question about your data:</label>
                    <textarea id="question" name="question" placeholder="e.g., List all users who placed an order over 100"></textarea>
                    <button type="submit">Generate SQL</button>
                </form>
                <div id="result"></div>
            </div>
            
            <script>
                document.getElementById('queryForm').onsubmit = function(e) {
                    e.preventDefault();
                    const question = document.getElementById('question').value;
                    
                    fetch('/query', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({question: question})
                    })
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('result').innerHTML = 
                            '<div class="result"><h3>Generated SQL:</h3><pre>' + data.sql + 
                            '</pre><h3>Results:</h3><pre>' + JSON.stringify(data.results, null, 2) + '</pre></div>';
                    });
                };
            </script>
        </body>
        </html>
        """
        
        @app.route('/')
        def index():
            return render_template_string(HTML_TEMPLATE, schema=copilot.schema_context)
        
        @app.route('/query', methods=['POST'])
        def query():
            data = request.json
            question = data.get('question', '')
            sql_query, results = copilot.ask_and_execute(question)
            return jsonify({'sql': sql_query, 'results': results})
        
        print("Starting web interface at http://localhost:5000")
        app.run(debug=True, port=5000)
        
    except ImportError:
        print("Flask not installed. Install with: pip install flask")
        print("Using CLI interface instead.")
        cli_interface()

if __name__ == "__main__":
    # Check if --web flag is provided
    if '--web' in os.sys.argv:
        web_interface()
    else:
        cli_interface()
