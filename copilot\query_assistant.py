import openai
import sqlite3
import os
from schema.extract_schema import extract_schema_from_db

class SQLCopilot:
    def __init__(self, db_path, api_key=None):
        self.db_path = db_path
        self.schema_context = extract_schema_from_db(db_path)
        
        # Set up OpenAI API key
        if api_key:
            openai.api_key = api_key
        elif os.getenv('OPENAI_API_KEY'):
            openai.api_key = os.getenv('OPENAI_API_KEY')
        else:
            print("Warning: No OpenAI API key provided. Set OPENAI_API_KEY environment variable or pass api_key parameter.")
    
    def ask_sql_copilot(self, user_question):
        """Generate SQL query based on user question and database schema"""
        prompt = f"""You are an expert SQL assistant.
Here is the database schema:
{self.schema_context}

Question: {user_question}
Write the SQL query:
"""
        try:
            response = openai.ChatCompletion.create(
                model="gpt-4",  # or "gpt-3.5-turbo"
                messages=[{"role": "user", "content": prompt}]
            )
            return response['choices'][0]['message']['content']
        except Exception as e:
            return f"Error generating SQL query: {str(e)}"
    
    def execute_query(self, sql_query):
        """Execute SQL query on the database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(sql_query)
            results = cursor.fetchall()
            conn.close()
            return results
        except Exception as e:
            return f"Error executing query: {str(e)}"
    
    def ask_and_execute(self, user_question):
        """Generate and execute SQL query based on user question"""
        sql_query = self.ask_sql_copilot(user_question)
        print(f"Generated SQL: {sql_query}")
        results = self.execute_query(sql_query)
        return sql_query, results

if __name__ == "__main__":
    # Example usage
    db_path = os.path.join(os.path.dirname(__file__), "..", "data", "dummy.db")
    
    if not os.path.exists(db_path):
        print("Database not found. Please run data/create_dummy_db.py first.")
        exit(1)
    
    copilot = SQLCopilot(db_path)
    
    # Example question
    question = "List all users who placed an order over 100"
    print(f"Question: {question}")
    sql_query, results = copilot.ask_and_execute(question)
    print(f"Results: {results}")
