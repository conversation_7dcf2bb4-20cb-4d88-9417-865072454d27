import sqlite3
import os
from schema.extract_schema import extract_schema_from_db
from copilot.local_sql_model import LocalSQLModel

class SQLCopilot:
    def __init__(self, db_path):
        self.db_path = db_path
        self.schema_context = extract_schema_from_db(db_path)

        # Initialize SQLCoder-7B model
        print("Initializing SQL Copilot with SQLCoder-7B...")
        self.sql_model = LocalSQLModel("./sqlcoder-7b")
        print("SQL Copilot ready!")
    
    def ask_sql_copilot(self, user_question):
        """Generate SQL query based on user question and database schema"""
        return self.sql_model.generate_sql(self.schema_context, user_question)
    
    def execute_query(self, sql_query):
        """Execute SQL query on the database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(sql_query)
            results = cursor.fetchall()
            conn.close()
            return results
        except Exception as e:
            return f"Error executing query: {str(e)}"
    
    def ask_and_execute(self, user_question):
        """Generate and execute SQL query based on user question"""
        sql_query = self.ask_sql_copilot(user_question)
        print(f"Generated SQL: {sql_query}")
        results = self.execute_query(sql_query)
        return sql_query, results

if __name__ == "__main__":
    # Example usage
    db_path = os.path.join(os.path.dirname(__file__), "..", "data", "northwind.db")

    if not os.path.exists(db_path):
        print("Database not found. Please ensure northwind.db exists in the data directory.")
        exit(1)

    copilot = SQLCopilot(db_path)

    # Example question
    question = "List all customers from Germany"
    print(f"Question: {question}")
    sql_query, results = copilot.ask_and_execute(question)
    print(f"Results: {results}")
