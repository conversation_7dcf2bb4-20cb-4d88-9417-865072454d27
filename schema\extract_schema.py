import sqlite3
import os

def get_schema(cursor):
    """Extract schema information from SQLite database"""
    tables = cursor.execute("SELECT name FROM sqlite_master WHERE type='table';").fetchall()
    schema = ""
    for (table,) in tables:
        schema += f"Table: {table}\n"
        columns = cursor.execute(f"PRAGMA table_info({table});").fetchall()
        for col in columns:
            schema += f"  - {col[1]} ({col[2]})\n"
        schema += "\n"
    return schema.strip()

def extract_schema_from_db(db_path):
    """Extract schema from database file"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    schema_context = get_schema(cursor)
    conn.close()
    return schema_context

if __name__ == "__main__":
    # Example usage
    db_path = os.path.join(os.path.dirname(__file__), "..", "data", "dummy.db")
    if os.path.exists(db_path):
        schema = extract_schema_from_db(db_path)
        print("Database Schema:")
        print(schema)
    else:
        print(f"Database not found at {db_path}")
        print("Please run data/create_dummy_db.py first to create the database.")
