{"messages": [{"role": "system", "content": "You are a helpful SQL assistant."}, {"role": "user", "content": "Schema:\nTable: users\n - id (INTEGER)\n - name (TEXT)\n - email (TEXT)\n\nTable: orders\n - id (INTEGER)\n - user_id (INTEGER)\n - product (TEXT)\n - price (REAL)\n\nQuestion: List all users who placed an order over 100"}, {"role": "assistant", "content": "SELECT DISTINCT u.name FROM users u JOIN orders o ON u.id = o.user_id WHERE o.price > 100;"}]}
{"messages": [{"role": "system", "content": "You are a helpful SQL assistant."}, {"role": "user", "content": "Schema:\nTable: users\n - id (INTEGER)\n - name (TEXT)\n - email (TEXT)\n\nTable: orders\n - id (INTEGER)\n - user_id (INTEGER)\n - product (TEXT)\n - price (REAL)\n\nQuestion: Show total spending per user"}, {"role": "assistant", "content": "SELECT u.name, SUM(o.price) as total_spending FROM users u LEFT JOIN orders o ON u.id = o.user_id GROUP BY u.id, u.name;"}]}
{"messages": [{"role": "system", "content": "You are a helpful SQL assistant."}, {"role": "user", "content": "Schema:\nTable: users\n - id (INTEGER)\n - name (TEXT)\n - email (TEXT)\n\nTable: orders\n - id (INTEGER)\n - user_id (INTEGER)\n - product (TEXT)\n - price (REAL)\n\nQuestion: Find the most expensive order"}, {"role": "assistant", "content": "SELECT o.*, u.name FROM orders o JOIN users u ON o.user_id = u.id WHERE o.price = (SELECT MAX(price) FROM orders);"}]}
{"messages": [{"role": "system", "content": "You are a helpful SQL assistant."}, {"role": "user", "content": "Schema:\nTable: users\n - id (INTEGER)\n - name (TEXT)\n - email (TEXT)\n\nTable: orders\n - id (INTEGER)\n - user_id (INTEGER)\n - product (TEXT)\n - price (REAL)\n\nQuestion: Count orders by user"}, {"role": "assistant", "content": "SELECT u.name, COUNT(o.id) as order_count FROM users u LEFT JOIN orders o ON u.id = o.user_id GROUP BY u.id, u.name;"}]}
