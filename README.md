# SQL Copilot - SQLCoder-7B Edition

A natural language to SQL query generator using the powerful SQLCoder-7B model. Ask questions in plain English and get high-quality SQL queries that work with your database - completely offline!

## Features

- 🤖 Natural language to SQL conversion using SQLCoder-7B (7 billion parameter model)
- 📊 Automatic database schema extraction
- 💻 CLI and web interface
- 🔍 Query execution and result display
- 🏠 Runs completely offline - no API keys needed
- ⚡ Intelligent fallback system for reliability
- 🗃️ Works with Northwind database out of the box

## Directory Structure

```
sql-copilot/
├── data/
│   └── northwind.db             # Northwind SQLite database
├── schema/
│   └── extract_schema.py        # Extract schema from database
├── copilot/
│   ├── query_assistant.py       # Main SQL Copilot class
│   └── local_sql_model.py       # SQLCoder-7B model implementation
├── sqlcoder-7b/                 # Local SQLCoder-7B model files
├── prompts/
│   └── examples.jsonl           # Training examples
├── app.py                       # CLI and web interface
├── requirements.txt             # Python dependencies
└── README.md                    # This file
```

## Quick Start

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Ensure you have the SQLCoder-7B model in the `./sqlcoder-7b` directory**

3. **Try the CLI interface:**
   ```bash
   # Single question
   python app.py -q "List all customers from Germany"

   # Interactive mode
   python app.py --interactive
   ```

4. **Try the web interface:**
   ```bash
   python app.py --web
   ```
   Then open http://localhost:5000 in your browser.

## Usage Examples

### CLI Examples
```bash
# Basic questions
python app.py -q "Show me all customers"
python app.py -q "List all products"
python app.py -q "Show all orders"

# Complex queries
python app.py -q "List all customers from Germany"
python app.py -q "Show customers from USA"
python app.py -q "List all employees"
python app.py -q "Show recent orders"

# Interactive mode
python app.py -i
```

### Python API
```python
from copilot.query_assistant import SQLCopilot

# Initialize with Northwind database
copilot = SQLCopilot('data/northwind.db')

# Generate SQL
sql = copilot.ask_sql_copilot("List all customers from Germany")
print(sql)

# Generate and execute
sql, results = copilot.ask_and_execute("Show all products")
print(f"SQL: {sql}")
print(f"Results: {results}")
```

## Database Schema

The Northwind database includes:

- **customers** - Customer information (company, contact, country, etc.)
- **orders** - Order details (dates, shipping info, etc.)
- **products** - Product catalog (name, price, stock, etc.)
- **employees** - Employee information
- **categories** - Product categories
- **suppliers** - Supplier information
- **order_details** - Order line items

## Configuration

### Using Your Own Database

Replace `data/northwind.db` with your own SQLite database:

```python
copilot = SQLCopilot('path/to/your/database.db')
```

### Model Architecture

The system uses a two-tier approach:

1. **SQLCoder-7B Model** - Primary SQL generation using the state-of-the-art 7B parameter model
2. **Intelligent Fallback** - Pattern-based SQL generation when the model is unavailable

### Model Performance

- **SQLCoder-7B**: High-quality SQL generation for complex queries
- **Fallback System**: Instant responses for common patterns
- **Automatic Switching**: Seamlessly falls back when needed
- **Offline Operation**: No internet connection required

## Fine-tuning

The `prompts/examples.jsonl` file contains training examples in OpenAI's fine-tuning format. You can:

1. Add more examples to improve accuracy
2. Use these examples for fine-tuning a custom model
3. Modify the prompt structure for better results

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add your improvements
4. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Troubleshooting

### Common Issues

1. **"Database not found"**: Ensure `northwind.db` exists in the `data/` directory
2. **Model loading issues**: The system will automatically fall back to pattern-based generation
3. **Import errors**: Install dependencies with `pip install -r requirements.txt`
4. **Memory issues**: The fallback system uses minimal memory
5. **Transformers conflicts**: The fallback system works without transformers library

### Getting Help

- Check the database schema: `python schema/extract_schema.py`
- Test the query assistant: `python copilot/query_assistant.py`
- Use interactive mode for debugging: `python app.py -i`
