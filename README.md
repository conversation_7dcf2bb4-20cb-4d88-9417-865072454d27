# SQL Copilot

A natural language to SQL query generator using LLM assistance. Ask questions in plain English and get SQL queries that work with your database.

## Features

- 🤖 Natural language to SQL conversion using OpenAI GPT models
- 📊 Automatic database schema extraction
- 💻 CLI and web interface
- 🔍 Query execution and result display
- 📝 Fine-tuning examples for custom training

## Directory Structure

```
sql-copilot/
├── data/
│   ├── dummy.db                 # SQLite database file
│   └── create_dummy_db.py       # Script to create sample database
├── schema/
│   └── extract_schema.py        # Extract schema from database
├── copilot/
│   └── query_assistant.py       # Main logic to generate SQL queries
├── prompts/
│   └── examples.jsonl           # Fine-tuning examples
├── app.py                       # CLI and web interface
├── requirements.txt             # Python dependencies
└── README.md                    # This file
```

## Quick Start

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up OpenAI API key:**
   ```bash
   export OPENAI_API_KEY="your-api-key-here"
   ```

3. **Create the dummy database:**
   ```bash
   python data/create_dummy_db.py
   ```

4. **Try the CLI interface:**
   ```bash
   # Single question
   python app.py -q "List all users who placed an order over 100"
   
   # Interactive mode
   python app.py --interactive
   ```

5. **Try the web interface:**
   ```bash
   python app.py --web
   ```
   Then open http://localhost:5000 in your browser.

## Usage Examples

### CLI Examples
```bash
# Basic question
python app.py -q "Show me all users"

# Complex query
python app.py -q "What is the total spending per user?"

# Interactive mode
python app.py -i
```

### Python API
```python
from copilot.query_assistant import SQLCopilot

# Initialize
copilot = SQLCopilot('data/dummy.db')

# Generate SQL
sql = copilot.ask_sql_copilot("List all users who placed an order over 100")
print(sql)

# Generate and execute
sql, results = copilot.ask_and_execute("Show total spending per user")
print(f"SQL: {sql}")
print(f"Results: {results}")
```

## Database Schema

The dummy database includes:

- **users** table: id, name, email
- **orders** table: id, user_id, product, price

## Configuration

### Using Your Own Database

Replace `data/dummy.db` with your own SQLite database:

```python
copilot = SQLCopilot('path/to/your/database.db')
```

### Using Different LLM Models

Modify the model in `copilot/query_assistant.py`:

```python
response = openai.ChatCompletion.create(
    model="gpt-3.5-turbo",  # or "gpt-4"
    messages=[{"role": "user", "content": prompt}]
)
```

## Fine-tuning

The `prompts/examples.jsonl` file contains training examples in OpenAI's fine-tuning format. You can:

1. Add more examples to improve accuracy
2. Use these examples for fine-tuning a custom model
3. Modify the prompt structure for better results

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add your improvements
4. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Troubleshooting

### Common Issues

1. **"Database not found"**: Run `python data/create_dummy_db.py` first
2. **"No OpenAI API key"**: Set the `OPENAI_API_KEY` environment variable
3. **Import errors**: Install dependencies with `pip install -r requirements.txt`

### Getting Help

- Check the database schema: `python schema/extract_schema.py`
- Test the query assistant: `python copilot/query_assistant.py`
- Use interactive mode for debugging: `python app.py -i`
