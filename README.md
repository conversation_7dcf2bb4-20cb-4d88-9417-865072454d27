# SQL Copilot - Local Language Model Edition

A natural language to SQL query generator using small local language models. Ask questions in plain English and get SQL queries that work with your database - no API keys required!

## Features

- Natural language to SQL conversion using local small language models
- Automatic database schema extraction
- CLI and web interface
- Query execution and result display
- Runs completely offline - no API keys needed
- Fast and lightweight models (DistilGPT-2, GPT-2)
- Intelligent fallback to rule-based generation

## Directory Structure

```
sql-copilot/
├── data/
│   ├── dummy.db                 # SQLite database file
│   └── create_dummy_db.py       # Script to create sample database
├── schema/
│   └── extract_schema.py        # Extract schema from database
├── copilot/
│   └── query_assistant.py       # Main logic to generate SQL queries
├── prompts/
│   └── examples.jsonl           # Fine-tuning examples
├── app.py                       # CLI and web interface
├── requirements.txt             # Python dependencies
└── README.md                    # This file
```

## Quick Start

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Create the dummy database:**
   ```bash
   python data/create_dummy_db.py
   ```

3. **Try the CLI interface:**
   ```bash
   # Single question
   python app.py -q "List all users who placed an order over 100"

   # Interactive mode
   python app.py --interactive

   # Use different model
   python app.py --model gpt2 -i
   ```

4. **Try the web interface:**
   ```bash
   python app.py --web
   ```
   Then open http://localhost:5000 in your browser.

## Usage Examples

### CLI Examples
```bash
# Basic questions
python app.py -q "Show me all users"
python app.py -q "List all orders"

# Complex queries
python app.py -q "What is the total spending per user?"
python app.py -q "List all users who placed an order over 100"
python app.py -q "Who ordered keyboard?"
python app.py -q "Show orders over 50"
python app.py -q "Count orders by user"
python app.py -q "What is the most expensive order?"

# Interactive mode
python app.py -i
```

### Python API
```python
from copilot.query_assistant import SQLCopilot

# Initialize with default model (distilgpt2)
copilot = SQLCopilot('data/dummy.db')

# Or specify a different model
copilot = SQLCopilot('data/dummy.db', model_name='gpt2')

# Generate SQL
sql = copilot.ask_sql_copilot("List all users who placed an order over 100")
print(sql)

# Generate and execute
sql, results = copilot.ask_and_execute("Show total spending per user")
print(f"SQL: {sql}")
print(f"Results: {results}")
```

## Database Schema

The dummy database includes:

- **users** table: id, name, email
- **orders** table: id, user_id, product, price

## Configuration

### Using Your Own Database

Replace `data/dummy.db` with your own SQLite database:

```python
copilot = SQLCopilot('path/to/your/database.db')
```

### Available Models

The system supports several approaches:

- **rule-based** (default) - Fast, reliable pattern matching
- **ollama** - Use local Ollama API (requires Ollama installation)
- **huggingface-api** - Use Hugging Face Inference API

```python
# Use different models
copilot = SQLCopilot('data/dummy.db', 'rule-based')     # Fast, reliable
copilot = SQLCopilot('data/dummy.db', 'ollama')         # Local LLM via Ollama
copilot = SQLCopilot('data/dummy.db', 'huggingface-api') # Cloud API
```

### Model Performance

- **Rule-based**: Instant responses, handles common SQL patterns very well
- **Ollama**: Good for complex queries, requires local setup
- **Hugging Face API**: Good quality, requires internet connection
- **Intelligent fallback**: External models fall back to rule-based on failure

## Fine-tuning

The `prompts/examples.jsonl` file contains training examples in OpenAI's fine-tuning format. You can:

1. Add more examples to improve accuracy
2. Use these examples for fine-tuning a custom model
3. Modify the prompt structure for better results

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add your improvements
4. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Troubleshooting

### Common Issues

1. **"Database not found"**: Run `python data/create_dummy_db.py` first
2. **Model download issues**: First run may take time to download models
3. **Import errors**: Install dependencies with `pip install -r requirements.txt`
4. **Memory issues**: Use `distilgpt2` for lower memory usage
5. **Slow performance**: Models will be faster after first load

### Getting Help

- Check the database schema: `python schema/extract_schema.py`
- Test the query assistant: `python copilot/query_assistant.py`
- Use interactive mode for debugging: `python app.py -i`
