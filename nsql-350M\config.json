{"_name_or_path": "nsql-350M", "activation_function": "gelu_new", "architectures": ["CodeGenForCausalLM"], "attn_pdrop": 0.0, "bos_token_id": 1, "embd_pdrop": 0.0, "eos_token_id": 50256, "gradient_checkpointing": false, "initializer_range": 0.02, "layer_norm_epsilon": 1e-05, "model_type": "codegen", "n_ctx": 2048, "n_embd": 1024, "n_head": 16, "n_inner": null, "n_layer": 20, "n_positions": 2048, "pad_token_id": 50256, "resid_pdrop": 0.0, "rotary_dim": 32, "scale_attn_weights": true, "summary_activation": null, "summary_first_dropout": 0.1, "summary_proj_to_labels": true, "summary_type": "cls_index", "summary_use_proj": true, "task_specific_params": {"text-generation": {"do_sample": true, "max_length": 50, "temperature": 1.0}}, "tie_word_embeddings": false, "tokenizer_class": "GPT2Tokenizer", "torch_dtype": "float32", "transformers_version": "4.28.1", "use_cache": true, "vocab_size": 51200}