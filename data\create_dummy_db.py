import sqlite3
import os

# Create data directory if it doesn't exist
os.makedirs(os.path.dirname(__file__), exist_ok=True)

# Create dummy database
conn = sqlite3.connect(os.path.join(os.path.dirname(__file__), "dummy.db"))
cursor = conn.cursor()

# Create schema
cursor.executescript("""
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    name TEXT,
    email TEXT
);

CREATE TABLE orders (
    id INTEGER PRIMARY KEY,
    user_id INTEGER,
    product TEXT,
    price REAL,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

INSERT INTO users (name, email) VALUES
('Alice', '<EMAIL>'),
('Bob', '<EMAIL>');

INSERT INTO orders (user_id, product, price) VALUES
(1, 'Keyboard', 50.0),
(2, 'Mouse', 25.0),
(1, 'Monitor', 200.0);
""")

conn.commit()
conn.close()

print("Dummy database created successfully at data/dummy.db")
